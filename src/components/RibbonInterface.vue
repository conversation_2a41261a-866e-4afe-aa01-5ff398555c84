<template>
  <div class="ribbon-interface">
    <!-- Tab Navigation -->
    <div class="ribbon-tabs">
      <button
        v-for="tab in tabs"
        :key="tab.id"
        class="ribbon-tab"
        :class="{ active: activeTab === tab.id }"
        @click="setActiveTab(tab.id)"
      >
        {{ tab.name }}
      </button>
    </div>

    <!-- Tab Content -->
    <div class="ribbon-content">
      <!-- 统计计算 Tab -->
      <div v-if="activeTab === 'statistics-computing'" class="ribbon-panel">
        <div class="ribbon-group">
          <div class="ribbon-buttons">
            <slot name="statistics-computing"></slot>
          </div>
        </div>
      </div>

      <!-- 数据清洗 Tab -->
      <div v-if="activeTab === 'data-cleansing'" class="ribbon-panel">
        <div class="ribbon-group">
          <div class="ribbon-buttons">
            <slot name="data-cleansing"></slot>
          </div>
        </div>
      </div>

      <!-- 指标/维度生成 Tab -->
      <div v-if="activeTab === 'create-metric-dimension'" class="ribbon-panel">
        <div class="ribbon-group">
          <div class="ribbon-buttons">
            <slot name="create-metric-dimension"></slot>
          </div>
        </div>
      </div>

      <!-- 表格生成 Tab -->
      <div v-if="activeTab === 'create-sheet'" class="ribbon-panel">
        <div class="ribbon-group">
          <div class="ribbon-buttons">
            <slot name="create-sheet"></slot>
          </div>
        </div>
      </div>

      <!-- 归因分析 Tab -->
      <div v-if="activeTab === 'attribution-analysis'" class="ribbon-panel">
        <div class="ribbon-group">
          <div class="ribbon-buttons">
            <slot name="attribution-analysis"></slot>
          </div>
        </div>
      </div>

      <!-- 深度分析 Tab -->
      <div v-if="activeTab === 'deep-analysis'" class="ribbon-panel">
        <div class="ribbon-group">
          <div class="ribbon-buttons">
            <slot name="deep-analysis"></slot>
          </div>
        </div>
      </div>

      <!-- 财务分析 Tab -->
      <div v-if="activeTab === 'financial-analysis'" class="ribbon-panel">
        <div class="ribbon-group">
          <div class="ribbon-buttons">
            <slot name="financial-analysis"></slot>
          </div>
        </div>
      </div>

      <!-- 投资分析 Tab -->
      <div v-if="activeTab === 'investment-analysis'" class="ribbon-panel">
        <div class="ribbon-group">
          <div class="ribbon-buttons">
            <slot name="investment-analysis"></slot>
          </div>
        </div>
      </div>

      <!-- 风险分析 Tab -->
      <div v-if="activeTab === 'risk-analysis'" class="ribbon-panel">
        <div class="ribbon-group">
          <div class="ribbon-buttons">
            <slot name="risk-analysis"></slot>
          </div>
        </div>
      </div>

      <!-- 数据可视化 Tab -->
      <div v-if="activeTab === 'visualization'" class="ribbon-panel">
        <div class="ribbon-group">
          <div class="ribbon-buttons">
            <slot name="visualization"></slot>
          </div>
        </div>
      </div>

      <!-- 其他 Tab -->
      <div v-if="activeTab === 'other'" class="ribbon-panel">
        <div class="ribbon-group">
          <div class="ribbon-buttons">
            <slot name="other"></slot>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const activeTab = ref('statistics-computing')

const tabs = [
  { id: 'statistics-computing', name: '统计计算' },
  { id: 'data-cleansing', name: '数据清洗' },
  { id: 'create-metric-dimension', name: '指标/维度生成' },
  { id: 'create-sheet', name: '表格生成' },
  { id: 'attribution-analysis', name: '归因分析' },
  { id: 'deep-analysis', name: '深度分析' },
  { id: 'financial-analysis', name: '财务分析' },
  { id: 'investment-analysis', name: '投资分析' },
  { id: 'risk-analysis', name: '风险分析' },
  { id: 'visualization', name: '数据可视化' },
  { id: 'other', name: '其他' },
]

const setActiveTab = (tabId) => {
  activeTab.value = tabId
}

defineExpose({
  activeTab,
  setActiveTab,
})
</script>

<style scoped>
.ribbon-interface {
  background: #ffffff;
  border-bottom: 1px solid #d1d5db;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.ribbon-tabs {
  display: flex;
  background: linear-gradient(to bottom, #f8f9fa 0%, #f8f9fa 100%);
  border-bottom: 1px solid #d1d5db;
  padding: 0 16px;
  position: relative;
}

.ribbon-tab {
  padding: 10px 24px;
  border: none;
  background: transparent;
  color: #495057;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.15s ease;
  position: relative;
  margin: 0 1px;
  border-radius: 4px 4px 0 0;
}

.ribbon-tab:hover:not(.active) {
  background: rgba(255, 255, 255, 0.7);
  color: #212529;
  border-bottom-color: #adb5bd;
}

.ribbon-tab.active {
  color: #0d6efd;
  border-bottom-color: #0d6efd;
  background: #ffffff;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1;
}

.ribbon-content {
  padding: 12px 16px;
  background: #ffffff;
  border-top: 1px solid #ffffff;
}

.ribbon-panel {
  display: flex;
  gap: 32px;
  align-items: flex-start;
  flex-wrap: wrap;
}

.ribbon-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 100px;
  position: relative;
  padding: 0 12px;
}

.ribbon-group:not(:last-child)::after {
  content: '';
  position: absolute;
  right: -16px;
  top: 8px;
  bottom: 8px;
  width: 1px;
  background: #e9ecef;
}

.ribbon-buttons {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  justify-content: center;
  align-items: flex-start;
}

/* Enhanced responsive design */
@media (max-width: 1024px) {
  .ribbon-panel {
    gap: 24px;
  }

  .ribbon-group {
    min-width: 80px;
    padding: 0 8px;
  }
}

@media (max-width: 768px) {
  .ribbon-tabs {
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    padding: 0 8px;
  }

  .ribbon-tabs::-webkit-scrollbar {
    display: none;
  }

  .ribbon-tab {
    padding: 8px 16px;
    font-size: 12px;
    white-space: nowrap;
  }

  .ribbon-content {
    padding: 12px 16px;
    min-height: 70px;
  }

  .ribbon-panel {
    flex-direction: column;
    gap: 16px;
  }

  .ribbon-group {
    min-width: auto;
    padding: 0;
  }

  .ribbon-group:not(:last-child)::after {
    display: none;
  }

  .ribbon-group-title {
    text-align: left;
  }

  .ribbon-buttons {
    justify-content: flex-start;
  }
}

@media (max-width: 480px) {
  .ribbon-content {
    padding: 8px 12px;
  }

  .ribbon-buttons {
    gap: 4px;
  }
}
</style>
